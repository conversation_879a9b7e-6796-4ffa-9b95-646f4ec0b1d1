import { Component, OnInit } from '@angular/core';

interface AccelerationResult {
  rank: number;
  logo: string;
  universityName: string;
  time: string; // saniye.salise formatında
}

@Component({
  selector: 'app-acceleration-results',
  templateUrl: './acceleration-results.component.html',
  styleUrls: ['./acceleration-results.component.scss']
})
export class AccelerationResultsComponent implements OnInit {

  // bread crumb items
  breadCrumbItems: Array<{}>;
  
  selectedCategory: string = '2025-ec-elektromobil';
  
  categories = [
    { value: '2025-ec-elektromobil', label: '2025 EC Elektromobil İvmelenme' },
    { value: '2025-ec-hidromobil', label: '2025 EC Hidromobil İvmelenme' },
    { value: '2025-ec-lise', label: '2025 EC Lise İvmelenme' }
  ];

  // Statik veriler
  elektromobilData: AccelerationResult[] = [
    {
      rank: 1,
      logo: 'assets/images/teams/team1.png',
      universityName: 'İstanbul Teknik Üniversitesi',
      time: '3.45'
    },
    {
      rank: 2,
      logo: 'assets/images/teams/team2.png',
      universityName: 'Orta Doğu Teknik Üniversitesi',
      time: '3.67'
    },
    {
      rank: 3,
      logo: 'assets/images/teams/team3.png',
      universityName: 'Boğaziçi Üniversitesi',
      time: '3.89'
    },
    {
      rank: 4,
      logo: 'assets/images/teams/team4.png',
      universityName: 'Yıldız Teknik Üniversitesi',
      time: '4.12'
    },
    {
      rank: 5,
      logo: 'assets/images/teams/team5.png',
      universityName: 'Gazi Üniversitesi',
      time: '4.34'
    }
  ];

  hidromobilData: AccelerationResult[] = [
    {
      rank: 1,
      logo: 'assets/images/teams/hydro1.png',
      universityName: 'Sabancı Üniversitesi',
      time: '4.23'
    },
    {
      rank: 2,
      logo: 'assets/images/teams/hydro2.png',
      universityName: 'Koç Üniversitesi',
      time: '4.45'
    },
    {
      rank: 3,
      logo: 'assets/images/teams/hydro3.png',
      universityName: 'Bilkent Üniversitesi',
      time: '4.67'
    },
    {
      rank: 4,
      logo: 'assets/images/teams/hydro4.png',
      universityName: 'Hacettepe Üniversitesi',
      time: '4.89'
    }
  ];

  liseData: AccelerationResult[] = [
    {
      rank: 1,
      logo: 'assets/images/teams/lise1.png',
      universityName: 'Galatasaray Lisesi',
      time: '5.12'
    },
    {
      rank: 2,
      logo: 'assets/images/teams/lise2.png',
      universityName: 'Robert Kolej',
      time: '5.34'
    },
    {
      rank: 3,
      logo: 'assets/images/teams/lise3.png',
      universityName: 'İstanbul Erkek Lisesi',
      time: '5.56'
    },
    {
      rank: 4,
      logo: 'assets/images/teams/lise4.png',
      universityName: 'Kabataş Erkek Lisesi',
      time: '5.78'
    },
    {
      rank: 5,
      logo: 'assets/images/teams/lise5.png',
      universityName: 'Üsküdar Amerikan Lisesi',
      time: '6.01'
    }
  ];

  currentResults: AccelerationResult[] = [];

  constructor() { }

  ngOnInit() {
    this.breadCrumbItems = [{ label: 'İvmelenme Sonuçları' }];
    this.loadResults();
  }

  onCategorySelect(event: any) {
    this.selectedCategory = event.target.value;
    this.loadResults();
  }

  loadResults() {
    switch (this.selectedCategory) {
      case '2025-ec-elektromobil':
        this.currentResults = this.elektromobilData;
        break;
      case '2025-ec-hidromobil':
        this.currentResults = this.hidromobilData;
        break;
      case '2025-ec-lise':
        this.currentResults = this.liseData;
        break;
      default:
        this.currentResults = this.elektromobilData;
    }
  }

  getCategoryLabel(): string {
    const category = this.categories.find(cat => cat.value === this.selectedCategory);
    return category ? category.label : '';
  }
}
